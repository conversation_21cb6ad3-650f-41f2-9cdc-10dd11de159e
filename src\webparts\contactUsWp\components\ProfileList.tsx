import * as React from 'react';
import { useEffect, useState } from 'react';
import { ISPFXContext } from "@pnp/common";
import { sp } from '@pnp/sp/presets/all';
import { IProfileWpProps } from './IProfileWpProps';
import styles from './ProfileList.module.scss';

export interface IContact {
  Id: number;
  Name: string;
  Designation: string;
  Department: string;
  PhotoUrl: string;
}

const ContactCardGrid: React.FC<IProfileWpProps> = (props) => {
  const [contacts, setContacts] = useState<IContact[]>([]);
 
  useEffect(() => {
    sp.setup({
      spfxContext: props.context as unknown as ISPFXContext
    });
  
    const fetchContacts = (): void => {
      sp.web.lists
        .getByTitle('ContactProfile')
        .items
        .select('Id', 'Name', 'Designation', 'Department', 'PhotoUrl')
        .get()
        .then((items: React.SetStateAction<IContact[]>) => {
          setContacts(items);
        })
        .catch((error: any) => {
          console.error("Failed to fetch contacts:", error);
        });
    };
  
    fetchContacts();  // Call fetchContacts as usual
  }, []);
  
  return (
    <div className={styles.grid}>
      {contacts.map((contact, index) => {
        const rowClass =
          index < 4 ? styles.firstRow :
          index < 8 ? styles.secondRow :
          '';

        return (
          <div
            key={contact.Id}
            className={`${styles.card} ${rowClass}`}>
            <div className={styles.cardInner}>
              <img src={contact.PhotoUrl} alt={contact.Name} className={styles.photo} />
              <div className={styles.details}>
                <div className={styles.name}>{contact.Name}</div>
                <div className={styles.designation}>{contact.Designation}</div>
                <div className={styles.department}>{contact.Department}</div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ContactCardGrid;
