.grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
}

.card {
  width: 25%;
  box-sizing: border-box;
  padding: 10px;
  position: relative;
}

.cardInner {
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  padding: 15px;
  display: flex;
  gap: 15px;
  align-items: center;
  border-radius: 8px;
  position: relative;
  z-index: 1;
}

.photo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 1px 0px 9px grey;
}

.details {
  display: flex;
  flex-direction: column;
}

.name {
  font-weight: bold;
  font-size: 16px;
}

.designation {
  font-size: 14px;
  color: #666;
}

.department {
  font-size: 13px;
  color: orange;
}

/* First row: arrow pointing down */
.firstRow::after {
  content: '';
  position: absolute;
  bottom: -10px; /* extend below the card */
  left: 65%;
  transform: translateX(-50%);
  border-width: 10px;
  border-style: solid;
  border-color: #ffa5007d transparent transparent transparent;
  z-index: 2; /* ensure it's on top */
}

/* Second row */
.secondRow {
  margin-top: -19px; /* Pull row up under the arrow */
  z-index: 1;
}

.secondRow .cardInner {
  background-color: #f9f9f9;
}
