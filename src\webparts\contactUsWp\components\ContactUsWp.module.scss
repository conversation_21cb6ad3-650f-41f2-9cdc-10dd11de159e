body{
  font-family: Georgia, 'Times New Roman', Times, serif !important;
}
.form {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  background-color: none;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.contactUsWp {
  padding: 12px;
}

.row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

.field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.field label {
  font-size: 14px;
  margin-bottom: 8px;
}

.field input,
.field select,
.field textarea {
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
 
}

.field textarea {
  resize: vertical;
}

.fieldFullWidth {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.fieldFullWidth label {
  font-size: 14px;
  margin-bottom: 8px;
}

.fieldFullWidth textarea {
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}

.submitButtonWrapper {
  display: flex;
  justify-content: flex-end;
}

.submitButton {
  background-color: #ff6e00;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-radius: 20px;
  width: 10%;

}

.submitButton:hover {
  background-color: #ff6e00;
}
.field input,
.field select,
.field textarea {
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  transition: border 0.3s ease, box-shadow 0.3s ease;
  color: #808080b3;
  font-family: Georgia, 'Times New Roman', Times, serif !important;
}

.field input:focus,
.field select:focus,
.field textarea:focus {
  outline: none;
  border: 1px solid #ff6e00;
  box-shadow: 0 0 4px rgba(255, 110, 0, 0.4);
}
.fieldFullWidth textarea:focus {
  outline: none;
  border: 1px solid #ff6e00;
  box-shadow: 0 0 4px rgba(255, 110, 0, 0.4);
}
.contactList{
    padding: 20px;
    background: #8080800a;
    margin-top: 50px;
    box-shadow: 0px 0px 6px #80808026;
}