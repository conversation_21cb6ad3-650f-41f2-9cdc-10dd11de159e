import * as React from 'react';
import styles from './ContactUsWp.module.scss';
import type { IContactUsWpProps } from './IContactUsWpProps';
import { ISPFXContext, sp } from '@pnp/sp/presets/all';
import ProfileContent from './ProfileList'

const ContactUsWp:React.FC<IContactUsWpProps> =(props)=>{

  const [AvailableTime, SetAvailableTime] = React.useState<string[]>([]);
  const [queries, setQueries] = React.useState<string[]>([]);
  React.useEffect(() => {
    sp.setup({
      spfxContext: props.context as unknown as ISPFXContext
    });
    sp.web.lists.getByTitle('ContactUs').items.select('AvailableTime', 'Query').get()
    .then((items: any[]) => {
      const uniqueAvailableTime = Array.from(new Set(items.map(i => i.AvailableTime).filter(Boolean)));
      const uniqueQueries = Array.from(new Set(items.map(i => i.Query).filter(Boolean)));

      SetAvailableTime(uniqueAvailableTime);
      setQueries(uniqueQueries);
    })
    .catch(console.error);     
  },[]);
  
  const [formData, setFormData] = React.useState({
    name: '',
    email: '',
    AvailableTime: '',
    Query: '',
    comment: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>):void => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent):void => {
    e.preventDefault();
    console.log(formData);
  };
  return(
  <><form className={styles.form} onSubmit={handleSubmit}>
        {/* Row 1 - Two Text Fields */}
        <div className={styles.row}>
          <div className={styles.field}>
            <input
              type="text"
              placeholder='Your Name'
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required />
          </div>
          <div className={styles.field}>
            <input
              type="email"
              id="email"
              placeholder='Your Email address'
              name="email"
              value={formData.email}
              onChange={handleChange}
              required />
          </div>
        </div>

        <div className={styles.row}>
          <div className={styles.field}>
            <select name="AvailableTime" value={formData.AvailableTime} onChange={handleChange} required>
              <option value="">Select AvailableTime</option>
              {AvailableTime.map((AvailableTime, idx) => (
                <option key={idx} value={AvailableTime}>{AvailableTime}</option>
              ))}
            </select>
          </div>
          <div className={styles.field}>
            <select name="Query" value={formData.Query} onChange={handleChange} required>
              <option value="">Query Related to?</option>
              {queries.map((query, idx) => (
                <option key={idx} value={query}>{query}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Row 3 - Comment Box */}
        <div className={styles.fieldFullWidth}>
          <textarea
            id="comment"
            name="comment"
            placeholder='Write your issues or feedback here…'
            value={formData.comment}
            onChange={handleChange}
            rows={4}
            required
          />
        </div>

        {/* Submit Button */}
        <div className={styles.submitButtonWrapper}>
          <button type="submit" className={styles.submitButton}>
            SEND
          </button>
        </div>
    <div className={styles.contactList}>
      <ProfileContent context={props.context}/>
    </div>
    </form>
    
    
    </>
  
  );
}
export default ContactUsWp;

